#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本用于读取Excel文件中的member_id数据，并生成3个SQL文件
每个文件包含几千个ID的查询语句
"""

import pandas as pd
import math
import os

def read_member_ids_from_excel(file_path):
    """
    从Excel文件中读取member_id数据
    """
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(file_path)
        
        # 打印列名以便调试
        print(f"Excel文件列名: {df.columns.tolist()}")
        print(f"数据行数: {len(df)}")
        
        # 查找包含member_id的列
        member_id_column = None
        for col in df.columns:
            if 'member_id' in col.lower() or 'memberid' in col.lower() or 'id' in col.lower():
                member_id_column = col
                break
        
        if member_id_column is None:
            # 如果没找到明确的member_id列，使用第一列
            member_id_column = df.columns[0]
            print(f"未找到明确的member_id列，使用第一列: {member_id_column}")
        else:
            print(f"使用列: {member_id_column}")
        
        # 提取member_id数据并去重
        member_ids = df[member_id_column].dropna().astype(str).unique().tolist()
        
        # 过滤掉空字符串和无效值
        member_ids = [mid for mid in member_ids if mid.strip() and mid.lower() != 'nan']
        
        print(f"提取到 {len(member_ids)} 个唯一的member_id")
        return member_ids
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return []

def generate_sql_template(member_ids_chunk, chunk_index):
    """
    生成SQL查询模板
    """
    # 将member_id列表转换为SQL IN子句格式
    ids_str = ",\n    ".join([f"'{mid}'" for mid in member_ids_chunk])
    
    sql_template = f"""SELECT DISTINCT 
    member_id,
    digital_id,
    last_login_country,
    first_operation_country,
    kyc_country,
    member_country,
    standard_language,
    origin
FROM dw_dim.dim_hierarchy_t_ourbit_member_a_daily
WHERE dt BETWEEN '20250208' AND '20250807'
  AND member_id IN (
    {ids_str}
  );"""
    
    return sql_template

def split_and_generate_sql_files(member_ids, output_dir="sql_files"):
    """
    将member_id列表分成3个文件并生成SQL
    """
    if not member_ids:
        print("没有找到有效的member_id数据")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    total_ids = len(member_ids)
    chunk_size = math.ceil(total_ids / 3)  # 分成3个文件
    
    print(f"总共 {total_ids} 个member_id，每个文件大约 {chunk_size} 个ID")
    
    for i in range(3):
        start_idx = i * chunk_size
        end_idx = min((i + 1) * chunk_size, total_ids)
        
        chunk = member_ids[start_idx:end_idx]
        
        if not chunk:  # 如果这个chunk为空，跳过
            continue
            
        sql_content = generate_sql_template(chunk, i + 1)
        
        filename = f"member_query_part_{i + 1}.sql"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        print(f"生成文件: {filepath} (包含 {len(chunk)} 个member_id)")

def main():
    # Excel文件路径
    excel_file = "地址数据 2.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: 找不到文件 {excel_file}")
        return
    
    print(f"开始处理文件: {excel_file}")
    
    # 读取member_id数据
    member_ids = read_member_ids_from_excel(excel_file)
    
    if not member_ids:
        print("未能提取到有效的member_id数据")
        return
    
    # 生成SQL文件
    split_and_generate_sql_files(member_ids)
    
    print("SQL文件生成完成！")

if __name__ == "__main__":
    main()
